<template>
  <div class="chat-container">
    <!-- 聊天头部区域 -->
    <div class="chat-header">
      <!-- 当前对话信息显示 -->
      <div class="chat-info">
        <!-- 对话标题，如果没有当前对话则显示"新对话" -->
        <h2 class="chat-title">{{ currentConversation?.title || '新对话' }}</h2>
        <!-- 对话最后更新时间，使用过滤器格式化显示 -->
        <span class="chat-time">
          {{ currentConversation?.updatedAt | formatTime }}
        </span>
      </div>

      <!-- 聊天操作按钮组 -->
      <div class="chat-actions">
        <!-- API连接状态组件 -->
        <!-- <ApiStatus /> -->
        <!-- 导出对话记录按钮 -->
        <el-button
          type="text"
          size="medium"
          @click="exportChat"
        >
          <i class="el-icon-download"></i>
          导出
        </el-button>
        <!-- 清空当前对话按钮 -->
        <el-button
          type="text"
          size="medium"
          @click="clearChat"
        >
          <i class="el-icon-delete"></i>
          清空
        </el-button>
        <!-- 跳转到设置页面按钮 -->
        <el-button
          type="text"
          size="medium"
          @click="$router.push('/main/settings')"
        >
          <i class="el-icon-setting"></i>
          设置
        </el-button>
      </div>
    </div>


    
    <!-- 消息列表容器 -->
    <div class="messages-container" ref="messagesContainer">
      <!-- 空状态显示：当没有消息时显示 -->
      <div v-if="currentMessages.length === 0" class="empty-state">
        <!-- 空状态图标 -->
        <div class="empty-icon">
          <i class="el-icon-chat-dot-round"></i>
        </div>
        <!-- 空状态提示文字 -->
        <h3>开始新的对话</h3>
        <p>向AI助手提问任何问题，我会尽力为您解答</p>

        <!-- 快速问题卡片区域 -->
        <div class="quick-questions">
          <h4>💡 试试这些问题：</h4>
          <div class="question-cards">
            <!-- 遍历快速问题列表，生成可点击的问题卡片 -->
            <div
              v-for="question in quickQuestions"
              :key="question.id"
              class="question-card"
              @click="sendQuickQuestion(question.text)"
            >
              <i :class="question.icon"></i>
              <span>{{ question.text }}</span>
            </div>
          </div>
        </div>
      </div>


      <!-- 消息列表：当有消息时显示 -->
      <div v-else class="messages-list">
        <!-- 遍历当前对话的所有消息 -->
        <div
          v-for="message in currentMessages"
          :key="message.id"
          class="message"
          :class="{ 'message-user': message.type === 'user', 'message-ai': message.type === 'ai' }"
        >
          <!-- 消息发送者头像 -->
          <div class="message-avatar">
            <!-- 根据消息类型显示不同图标：用户或AI -->
            <i v-if="message.type === 'user'" class="el-icon-user"></i>
            <i v-else class="el-icon-cpu"></i>
          </div>

          <!-- 消息内容区域 -->
          <div class="message-content">
            <!-- AI消息耗时显示区域 - 固定显示 -->
            <div v-if="message.type === 'ai'" class="ai-response-header">
              <!-- 实时耗时显示（流式输入时） -->
              <div v-if="message.isStreaming" class="ai-timing-live">
                <div class="timing-content">
                  <i class="el-icon-loading timing-icon-animated"></i>
                  <span class="timing-prefix">用时</span>
                  <span class="timing-value live">{{ formatElapsedTime(getMessageElapsedTime(message)) }}</span>
                </div>
                <div class="timing-progress">
                  <div class="progress-bar"></div>
                </div>
              </div>

              <!-- 固定耗时显示（完成后） - 始终显示 -->
              <div v-else class="ai-timing-completed">
                <div class="timing-content">
                  <i class="el-icon-time"></i>
                  <span class="timing-prefix">用时</span>
                  <span class="timing-value completed">{{ formatElapsedTime(message.responseTime || getMessageElapsedTime(message)) }}</span>
                  <span class="status-indicator complete">
                    <i class="el-icon-check"></i>
                    完成
                  </span>
                </div>
              </div>
            </div>

            <!-- 消息文本内容，支持Markdown和HTML格式化 -->
            <div class="message-text" :class="{
              'typing-effect': message.isTyping,
              'streaming-effect': message.isStreaming,
              'error-message': message.isError
            }">
              <!-- 如果是AI消息且包含Markdown格式，使用Markdown渲染器 -->
              <MarkdownRenderer
                v-if="message.type === 'ai' && isMarkdownContent(message.content)"
                :content="message.content"
                :enable-highlight="true"
              />

              <!-- 否则使用普通的HTML格式化 -->
              <div v-else v-html="formatMessage(message.content)"></div>

              <!-- 流式输入指示器 -->
              <div v-if="message.isStreaming" class="streaming-indicator">
                <span class="cursor-blink">|</span>
              </div>
            </div>
            <!-- 消息发送时间，使用过滤器格式化 -->
            <div class="message-time">
              {{ message.timestamp | formatTime }}

              <!-- AI消息显示响应时间 -->
              <span v-if="message.type === 'ai' && message.responseTime" class="response-time">
                <i class="el-icon-time"></i>
                {{ formatElapsedTime(message.responseTime) }}
              </span>

              <!-- 消息状态标识 -->
              <span v-if="message.isStreaming" class="status-badge streaming">
                <i class="el-icon-loading"></i>
                流式输入中
              </span>

              <span v-else-if="message.isInterrupted" class="status-badge interrupted">
                <i class="el-icon-video-pause"></i>
                已中断
              </span>

              <span v-else-if="message.isError" class="status-badge error">
                <i class="el-icon-warning"></i>
                失败
              </span>

              <span v-else-if="message.isComplete" class="status-badge complete">
                <i class="el-icon-check"></i>
                完成
              </span>
            </div>
          </div>

          <!-- 消息操作按钮 -->
          <div class="message-actions">
            <!-- AI消息流式输出时的中断按钮 -->
            <el-button
              v-if="message.type === 'ai' && message.isStreaming"
              type="text"
              size="medium"
              @click="stopAIResponse"
              title="中断AI回复"
              class="stop-message-btn large-icon"
            >
              <i class="el-icon-video-pause"></i>
            </el-button>

            <!-- AI消息中断后的重试按钮 -->
            <el-button
              v-if="message.type === 'ai' && message.isInterrupted"
              type="text"
              size="medium"
              @click="retryMessage(message)"
              title="重试AI回复"
              class="retry-message-btn large-icon"
            >
              <i class="el-icon-refresh"></i>
            </el-button>

            <!-- AI消息错误后的重试按钮 -->
            <el-button
              v-if="message.type === 'ai' && message.isError"
              type="text"
              size="medium"
              @click="retryMessage(message)"
              title="重试AI回复"
              class="retry-message-btn large-icon"
            >
              <i class="el-icon-refresh"></i>
            </el-button>

            <!-- 复制消息内容按钮 -->
            <el-button
              v-if="!message.isStreaming"
              type="text"
              size="mini"
              @click="copyMessage(message.content)"
              title="复制消息"
            >
              <i class="el-icon-document-copy"></i>
            </el-button>
          </div>
        </div>

        <!-- AI正在输入指示器：当AI正在生成回复时显示 -->
        <div v-if="localLoading" class="message message-ai">
          <div class="message-avatar">
            <i class="el-icon-cpu"></i>
          </div>
          <div class="message-content">
            <!-- AI思考中的状态显示 -->
            <div class="ai-thinking">
              <div class="thinking-header">
                <div class="thinking-text">
                  <i class="el-icon-loading"></i>
                  AI正在思考中...
                </div>
                <!-- 中断按钮 -->
                <el-button
                  type="text"
                  size="mini"
                  @click="stopAIResponse"
                  class="stop-btn"
                  title="中断AI回复"
                >
                  <i class="el-icon-video-pause"></i>
                  中断
                </el-button>
              </div>
              <!-- 实时计时器显示 -->
              <div class="thinking-timer">
                <span class="timer-label">已用时:</span>
                <span class="timer-value">{{ formatElapsedTime(elapsedTime) }}</span>
              </div>
              <!-- 打字动画效果 -->
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 回到底部按钮 -->
    <transition name="fade">
      <div
        v-if="showScrollToBottom"
        class="scroll-to-bottom"
        @click="scrollToBottom"
        title="回到底部"
      >
        <i class="el-icon-bottom"></i>
      </div>
    </transition>

    <!-- 消息输入区域 -->
    <div class="input-area">
      <div class="input-container">
        <!-- 消息输入框：支持多行文本，自动调整高度 -->
        <el-input
          ref="messageInput"
          v-model="inputMessage"
          type="textarea"
          :rows="1"
          :autosize="{ minRows: 1, maxRows: 4 }"
          placeholder="输入你的问题..."
          class="message-input"
          :disabled="localLoading"
          @keydown.native="handleKeyDown"
        />

        <!-- 按钮组：发送和暂停按钮 -->
        <div class="button-group">
          <!-- 暂停按钮：只在AI正在回复时显示 -->
          <el-button
            v-if="localLoading || hasStreamingMessage"
            type="danger"
            class="stop-input-btn"
            @click="stopAIResponse"
            title="中断AI回复"
          >
            <i class="el-icon-video-pause"></i>
          </el-button>

          <!-- 发送消息按钮：当输入为空或正在加载时禁用 -->
          <el-button
            type="primary"
            class="send-btn"
            :disabled="!inputMessage.trim() || localLoading"
            @click="sendMessage"
          >
            <i class="el-icon-s-promotion"></i>
          </el-button>
        </div>
      </div>

      <!-- 输入提示信息 -->
      <div class="input-tips">
        <div class="tips-left">
          <span>按 Enter 发送，Shift + Enter 换行</span>
        </div>
        <div class="tips-right">
          <!-- VIP状态显示 -->
          <div class="vip-status">
            <el-tag
              v-if="isVip"
              type="warning"
              size="mini"
              class="vip-badge"
            >
              <i class="el-icon-star-on"></i>
              {{ currentVipPlan.name }}
            </el-tag>
            <el-tag
              v-else
              type="info"
              size="mini"
              class="free-user-badge"
              @click="$router.push('/main/vip-plans')"
            >
              <i class="el-icon-user"></i>
              免费用户
            </el-tag>
            <span v-if="remainingMessages >= 0" class="message-count">
              今日剩余: {{ remainingMessages === -1 ? '无限' : remainingMessages }}
            </span>
            <!-- 免费用户升级提示 -->
            <el-button
              v-if="!isVip"
              type="text"
              size="mini"
              class="upgrade-hint-btn"
              @click="$router.push('/main/vip-plans')"
            >
              <i class="el-icon-star-on"></i>
              升级VIP
            </el-button>
          </div>
        </div>
      </div>

      <!-- VIP限制提示 -->
      <div v-if="sendLimitMessage" class="limit-warning">
        <i class="el-icon-warning"></i>
        <span>{{ sendLimitMessage }}</span>
        <el-button
          type="text"
          size="mini"
          @click="$router.push('/main/vip-plans')"
        >
          立即升级
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import ApiStatus from '@/components/ApiStatus.vue'
import MarkdownRenderer from '@/components/MarkdownRenderer.vue'
// 保留网络工具用于UI层的错误处理和状态显示
import { formatNetworkError, isOnline } from '@/utils/network'

/**
 * 聊天对话页面组件
 * 功能：
 * 1. 显示当前对话的消息列表
 * 2. 支持发送消息给AI并接收回复
 * 3. 提供快速问题卡片
 * 4. 支持导出、清空对话等操作
 * 5. 显示API连接状态
 */
export default {
  name: 'Chat',
  components: {
    MarkdownRenderer // Markdown渲染组件
    // ApiStatus // API状态显示组件
  },
  data() {
    return {
      // 用户输入的消息内容
      inputMessage: '',
      // 本地加载状态（替代全局加载状态）
      localLoading: false,
      // 计时相关数据
      requestStartTime: null, // 请求开始时间
      elapsedTime: 0, // 已用时间（秒）
      timerInterval: null, // 计时器间隔ID
      // 键盘事件处理器
      keydownHandler: null,
      // 回到底部按钮显示控制
      showScrollToBottom: false,
      // 快速问题列表：为用户提供常见问题的快速入口
      quickQuestions: [
        {
          id: 1,
          icon: 'el-icon-cpu',
          text: '什么是人工智能？'
        },
        {
          id: 2,
          icon: 'el-icon-edit',
          text: '如何学习编程？'
        },
        {
          id: 3,
          icon: 'el-icon-star-on',
          text: 'Vue.js 有什么特点？'
        },
        {
          id: 4,
          icon: 'el-icon-question',
          text: '前端开发趋势如何？'
        }
      ]
    }
  },
  computed: {
    // 从Vuex获取聊天相关的状态数据（移除isLoading，改用本地状态）
    ...mapGetters('chat', ['currentConversation']),
    ...mapGetters('user', ['vipLevel', 'isVip', 'remainingMessages', 'currentVipPlan']),

    /**
     * 当前对话ID
     * 从路由参数中获取对话ID
     * @returns {string} 对话ID
     */
    conversationId() {
      return this.$route.params.id
    },

    /**
     * 当前对话的消息列表
     * 直接基于路由中的对话ID获取消息，确保显示正确的消息
     * @returns {Array} 消息列表
     */
    currentMessages() {
      const conversationId = this.conversationId
      if (!conversationId) return []

      return this.$store.state.chat.messages[conversationId] || []
    },

    /**
     * 检查是否有正在流式输出的消息
     * @returns {boolean} 是否有流式消息
     */
    hasStreamingMessage() {
      return this.currentMessages.some(message => message.isStreaming)
    },

    /**
     * 检查是否可以发送消息
     * @returns {boolean} 是否可以发送
     */
    canSendMessage() {
      // 检查每日消息限制
      if (this.remainingMessages === 0) {
        return false
      }
      return true
    },

    /**
     * 获取发送限制提示信息
     * @returns {string} 提示信息
     */
    sendLimitMessage() {
      if (this.remainingMessages === 0) {
        return `今日消息已用完，升级VIP获得更多消息额度`
      }
      if (this.remainingMessages > 0 && this.remainingMessages <= 5) {
        return `今日还可发送 ${this.remainingMessages} 条消息`
      }
      return ''
    }

  },
  async mounted() {
    // 确保状态同步
    if (this.conversationId && this.conversationId !== this.$store.state.chat.currentConversationId) {
      this.$store.commit('chat/SET_CURRENT_CONVERSATION', this.conversationId)
    }

    // 从后台加载历史消息
    if (this.conversationId) {
      try {
        await this.$store.dispatch('chat/loadMessages', this.conversationId)
      } catch (error) {
        console.warn('加载历史消息失败:', error)
      }
    }

    // 滚动到底部显示最新消息
    this.$nextTick(() => {
      this.scrollToBottom()
      // 设置滚动监听器
      this.setupScrollListener()
    })

    // 注释：现在使用.native修饰符，不需要手动添加监听器
    // this.$nextTick(() => {
    //   this.setupKeyboardListener()
    // })
  },

  beforeDestroy() {
    // 清理键盘事件监听器
    this.removeKeyboardListener()

    // 清理滚动监听器
    this.removeScrollListener()

    // 清理SSE连接
    this.cleanupSSEConnections()
  },

  watch: {
    /**
     * 监听对话ID变化
     * 当路由参数中的对话ID发生变化时，更新当前对话
     */
    conversationId: {
      immediate: true, // 立即执行一次
      async handler(newId, oldId) {
        if (newId) {
          // 设置当前对话ID到Vuex状态中
          this.$store.commit('chat/SET_CURRENT_CONVERSATION', newId)

          // 确保加载状态重置（防止从其他对话页面切换过来时状态残留）
          if (this.localLoading) {
            this.localLoading = false
            if (this.timerInterval) {
              clearInterval(this.timerInterval)
              this.timerInterval = null
            }
          }

          // 当对话ID变化时，从后台重新加载消息（避免重复加载）
          if (newId !== oldId) {
            try {
              await this.$store.dispatch('chat/loadMessages', newId)
              // 加载完成后滚动到底部
              this.$nextTick(() => {
                this.scrollToBottom()
              })
            } catch (error) {
              console.warn('切换对话时加载消息失败:', error)
            }
          }
        }
      }
    },

    /**
     * 监听消息列表变化
     * 当有新消息时，自动滚动到底部
     */
    currentMessages() {
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

  },
  methods: {
    // 从Vuex导入发送消息的action，重命名避免冲突
    ...mapActions('chat', { sendMessageToStore: 'sendMessage' }),

    /**
     * 检查当前组件是否对应正确的对话
     * @returns {boolean} 是否匹配
     */
    isCurrentConversationActive() {
      const routeConversationId = this.conversationId
      const storeConversationId = this.$store.state.chat.currentConversationId
      return routeConversationId === storeConversationId
    },

    /**
     * 发送用户输入的消息
     * 验证输入内容，调用Vuex action发送消息给AI
     */
    async sendMessage() {
      // 基本验证
      if (!this.inputMessage.trim() || this.localLoading) return

      if (!this.conversationId) {
        this.$message.error('无效的对话ID，请刷新页面或选择一个对话')
        return
      }

      // VIP权限检查
      if (!this.canSendMessage) {
        this.$confirm(
          '今日消息额度已用完，升级VIP可获得更多消息额度。是否立即升级？',
          '消息额度不足',
          {
            confirmButtonText: '立即升级',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          this.$router.push('/main/vip-plans')
        }).catch(() => {
          // 用户取消
        })
        return
      }

      // 获取输入内容并清空输入框
      const content = this.inputMessage.trim()
      this.inputMessage = ''

      try {
        // 设置加载状态和开始计时
        this.localLoading = true
        this.startTimer()

        // 确保store状态同步
        this.$store.commit('chat/SET_CURRENT_CONVERSATION', this.conversationId)

        // 添加用户消息
        const userMessage = {
          id: 'msg_' + Date.now(),
          type: 'user',
          content: content,
          timestamp: new Date().toISOString()
        }
        this.$store.commit('chat/ADD_MESSAGE', {
          conversationId: this.conversationId,
          message: userMessage
        })

        // 滚动到底部显示新消息
        this.$nextTick(() => {
          this.scrollToBottom()
        })

        // 调用store action处理AI回复
        console.log('🔄 调用store sendMessage action')
        const result = await this.sendMessageToStore({
          conversationId: this.conversationId,
          content: content,
          skipUserMessage: true,
          // 传递回调函数来处理流式开始
          onStreamStart: () => {
            // 当开始接收流式数据时，隐藏loading状态
            this.localLoading = false
          }
        })

        // 停止计时
        const totalTime = this.stopTimer()
        console.log('📊 Store返回结果:', result)
        console.log('⏱️ 总耗时:', totalTime, '秒')

        if (result && result.success) {
          // 更新每日使用量
          this.$store.commit('user/UPDATE_DAILY_USAGE', {
            messageCount: 1,
            model: 'basic' // 这里可以根据实际使用的模型来设置
          })

          let responseType = 'AI回复'
          if (result.isLocal) {
            responseType = '本地模拟回复'
          } else if (result.isTimeout) {
            responseType = 'AI响应超时'
          }

          console.log('✅ 显示成功消息:', responseType)

          if (result.isTimeout) {
            this.$message.warning(`${responseType}，耗时 ${this.formatElapsedTime(totalTime)}`)
          } else {
            this.$message.success(`${responseType}完成，耗时 ${this.formatElapsedTime(totalTime)}`)
          }
        } else if (result && result.success === false) {
          // 明确的失败情况
          console.log('⚠️ 显示失败消息')
          this.$message.error(`AI回复失败，耗时 ${this.formatElapsedTime(totalTime)}`)
        } else {
          // 未知结果，但不显示错误（可能是正常的无返回值情况）
          console.warn('❓ AI回复结果未知:', result)
        }

        // 滚动到底部显示AI回复
        this.$nextTick(() => {
          this.scrollToBottom()
        })

      } catch (error) {
        console.error("发送消息失败:", error)

        const totalTime = this.stopTimer()
        this.$message.error(`发送失败: ${error.message} (耗时 ${this.formatElapsedTime(totalTime)})`)

      } finally {
        // 重置加载状态
        this.localLoading = false
      }
    },

    /**
     * 发送快速问题
     * 用户点击快速问题卡片时调用
     * @param {string} question - 问题文本
     */
    async sendQuickQuestion(question) {
      // 检查是否正在加载中
      if (this.localLoading) return

      // 设置输入框内容并发送
      this.inputMessage = question
      await this.sendMessage()
    },

    /**
     * 设置键盘事件监听器
     */
    setupKeyboardListener() {
      // 获取textarea元素
      const textareaEl = this.$refs.messageInput?.$refs?.textarea
      if (textareaEl) {
        console.log('✅ 找到textarea元素，添加键盘监听器')
        this.keydownHandler = this.handleKeyDown.bind(this)
        textareaEl.addEventListener('keydown', this.keydownHandler)
      } else {
        console.warn('❌ 未找到textarea元素')
      }
    },

    /**
     * 移除键盘事件监听器
     */
    removeKeyboardListener() {
      const textareaEl = this.$refs.messageInput?.$refs?.textarea
      if (textareaEl && this.keydownHandler) {
        textareaEl.removeEventListener('keydown', this.keydownHandler)
        this.keydownHandler = null
      }
    },

    /**
     * 统一处理键盘事件
     * Enter发送消息，Shift+Enter换行
     */
    handleKeyDown(event) {
      console.log('🎹 键盘事件捕获:', {
        key: event.key,
        shiftKey: event.shiftKey,
        ctrlKey: event.ctrlKey,
        target: event.target.tagName,
        inputValue: this.inputMessage,
        loading: this.localLoading
      })

      // 检查是否按下了Enter键
      if (event.key === 'Enter') {
        console.log('🔍 检测到Enter键')

        // 如果同时按下了Shift键，允许换行
        if (event.shiftKey) {
          console.log('📝 Shift+Enter: 允许换行')
          // 不阻止默认行为，允许换行
          return true
        } else {
          // 单独按Enter键，发送消息
          console.log('🚀 Enter键: 尝试发送消息')
          console.log('🔍 当前状态检查:', {
            hasContent: !!this.inputMessage.trim(),
            loading: this.localLoading,
            inputLength: this.inputMessage.length
          })

          // 阻止默认的换行行为
          event.preventDefault()
          event.stopPropagation()

          // 检查是否有内容且不在加载中
          if (this.inputMessage.trim() && !this.localLoading) {
            console.log('✅ 条件满足，发送消息')
            this.sendMessage()
          } else {
            console.log('❌ 发送被阻止:', {
              hasContent: !!this.inputMessage.trim(),
              loading: this.localLoading,
              reason: !this.inputMessage.trim() ? '输入为空' : '正在加载中'
            })
          }

          return false
        }
      }
    },

    /**
     * 开始计时器
     * 记录请求开始时间并启动实时计时
     */
    startTimer() {
      this.requestStartTime = Date.now()
      this.elapsedTime = 0

      // 每100毫秒更新一次显示的时间
      this.timerInterval = setInterval(() => {
        if (this.requestStartTime) {
          this.elapsedTime = (Date.now() - this.requestStartTime) / 1000
        }
      }, 100)
    },

    /**
     * 停止计时器
     * 清除计时器并返回总耗时
     * @returns {number} 总耗时（秒）
     */
    stopTimer() {
      if (this.timerInterval) {
        clearInterval(this.timerInterval)
        this.timerInterval = null
      }

      const totalTime = this.requestStartTime ? (Date.now() - this.requestStartTime) / 1000 : 0
      this.requestStartTime = null
      this.elapsedTime = 0

      return totalTime
    },

    /**
     * 格式化耗时显示
     * @param {number} seconds - 秒数
     * @returns {string} 格式化后的时间字符串
     */
    formatElapsedTime(seconds) {
      if (seconds < 1) {
        return `${Math.round(seconds * 1000)}ms`
      } else if (seconds < 60) {
        return `${seconds.toFixed(1)}s`
      } else {
        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = (seconds % 60).toFixed(1)
        return `${minutes}m ${remainingSeconds}s`
      }
    },

    /**
     * 获取消息的实时耗时
     * @param {Object} message - 消息对象
     * @returns {number} 耗时（秒）
     */
    getMessageElapsedTime(message) {
      if (!message || !message.startTime) {
        return 0
      }
      return (Date.now() - message.startTime) / 1000
    },


    
    /**
     * 设置滚动监听器
     * 监听消息容器的滚动事件，控制回到底部按钮的显示
     */
    setupScrollListener() {
      const container = this.$refs.messagesContainer
      if (container) {
        this.scrollHandler = this.handleScroll.bind(this)
        container.addEventListener('scroll', this.scrollHandler)
      }
    },

    /**
     * 移除滚动监听器
     */
    removeScrollListener() {
      const container = this.$refs.messagesContainer
      if (container && this.scrollHandler) {
        container.removeEventListener('scroll', this.scrollHandler)
        this.scrollHandler = null
      }
    },

    /**
     * 处理滚动事件
     * 判断是否需要显示回到底部按钮
     */
    handleScroll() {
      const container = this.$refs.messagesContainer
      if (container) {
        // 计算是否接近底部（距离底部小于100px时认为在底部）
        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100
        this.showScrollToBottom = !isNearBottom
      }
    },

    /**
     * 滚动消息列表到底部
     * 当有新消息时自动滚动，确保用户看到最新消息
     */
    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
        // 滚动到底部后隐藏按钮
        this.showScrollToBottom = false
      }
    },

    /**
     * 检测内容是否包含Markdown格式
     * @param {string} content - 消息内容
     * @returns {boolean} 是否包含Markdown格式
     */
    isMarkdownContent(content) {
      if (!content) return false

      // 检测常见的Markdown语法
      const markdownPatterns = [
        /^#{1,6}\s+.+$/m,           // 标题 # ## ### 等
        /\*\*[^*]+\*\*/,           // 粗体 **text**
        /\*[^*]+\*/,               // 斜体 *text*
        /^[-*+]\s+.+$/m,           // 无序列表 - * +
        /^\d+\.\s+.+$/m,           // 有序列表 1. 2. 3.
        /```[\s\S]*?```/,          // 代码块 ```code```
        /`[^`]+`/,                 // 行内代码 `code`
        /^\s*\|.+\|/m,             // 表格 |col1|col2|
        /^>\s+.+$/m,               // 引用 > text
        /\[.+\]\(.+\)/,            // 链接 [text](url)
        /!\[.*\]\(.+\)/,           // 图片 ![alt](url)
        /^---+$/m,                 // 分隔线 ---
        /~~[^~]+~~/                // 删除线 ~~text~~
      ]

      // 如果匹配任何一个Markdown模式，则认为是Markdown内容
      return markdownPatterns.some(pattern => pattern.test(content))
    },

    /**
     * 格式化消息内容
     * 将换行符转换为HTML的<br>标签，支持多行显示
     * @param {string} content - 原始消息内容
     * @returns {string} 格式化后的HTML内容
     */
    formatMessage(content) {
      // 简单的消息格式化，将换行符转换为<br>标签
      // 对于非Markdown内容使用此方法
      return content.replace(/\n/g, '<br>')
    },

    /**
     * 复制消息内容到剪贴板
     * @param {string} content - 要复制的消息内容
     */
    copyMessage(content) {
      // 使用现代浏览器的剪贴板API
      navigator.clipboard.writeText(content).then(() => {
        this.$message.success('已复制到剪贴板')
      }).catch(() => {
        // 复制失败时的降级处理
        this.$message.error('复制失败')
      })
    },


    
    /**
     * 清空当前对话的所有消息
     * 需要用户确认后才执行清空操作
     */
    async clearChat() {
      try {
        // 显示确认对话框
        await this.$confirm('确定要清空当前对话吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 检查对话ID是否有效
        if (!this.conversationId) {
          this.$message.error('无效的对话ID，无法清空对话')
          return
        }

        // 调用后台接口清空对话
        try {
          const { clearConversation } = await import('@/api/chat')
          await clearConversation(this.conversationId)

          // 后台清空成功后，同步清空前端状态
          this.$store.commit('chat/SET_MESSAGES', {
            conversationId: this.conversationId,
            messages: []
          })

          this.$message.success('对话已清空')
        } catch (apiError) {
          console.error('调用清空对话API失败:', apiError)

          // API调用失败时，仍然清空前端状态（降级处理）
          this.$store.commit('chat/SET_MESSAGES', {
            conversationId: this.conversationId,
            messages: []
          })

          this.$message.warning('对话已在本地清空，但服务器同步可能失败')
        }
      } catch (error) {
        // 用户取消操作，不做任何处理
        if (error !== 'cancel') {
          console.error('清空对话失败:', error)
        }
      }
    },

    /**
     * 导出当前对话记录为文本文件
     * 将对话内容格式化后下载为.txt文件
     */
    exportChat() {
      // 检查是否有消息可以导出
      if (this.currentMessages.length === 0) {
        this.$message.warning('当前对话为空，无法导出')
        return
      }

      // 格式化对话内容
      const content = this.currentMessages.map(msg => {
        const sender = msg.type === 'user' ? '用户' : 'AI助手'
        const time = this.$options.filters.formatTime(msg.timestamp)
        return `[${time}] ${sender}: ${msg.content}`
      }).join('\n\n')

      // 创建下载链接并触发下载
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${this.currentConversation?.title || '对话记录'}.txt`
      a.click()
      URL.revokeObjectURL(url) // 清理内存

      this.$message.success('对话已导出')
    },

    /**
     * 中断AI回复
     * 停止当前的流式连接并重置状态
     */
    async stopAIResponse() {
      try {
        console.log('🛑 开始中断AI回复')

        // 停止计时器
        const totalTime = this.stopTimer()

        // 立即重置加载状态
        this.localLoading = false

        // 立即停止SSE连接
        try {
          const { stopStreamChat } = await import('@/utils/sse')
          if (this.conversationId) {
            stopStreamChat(this.conversationId)
            console.log('🛑 SSE连接已断开，对话ID:', this.conversationId)
          }
        } catch (error) {
          console.error('停止SSE连接失败:', error)
        }

        // 查找当前正在流式输出的AI消息并标记为中断
        const messages = this.currentMessages
        const streamingMessage = messages.find(m => m.type === 'ai' && m.isStreaming)

        if (streamingMessage) {
          console.log('🛑 找到流式消息，标记为中断:', streamingMessage.id)

          // 更新消息状态为中断
          this.$store.commit('chat/UPDATE_MESSAGE_STATUS', {
            conversationId: this.conversationId,
            messageId: streamingMessage.id,
            isStreaming: false,
            isInterrupted: true
          })

          // 如果消息内容为空，添加中断提示
          if (!streamingMessage.content.trim()) {
            this.$store.commit('chat/UPDATE_MESSAGE_CONTENT', {
              conversationId: this.conversationId,
              messageId: streamingMessage.id,
              content: '⏸️ **回复已中断**\n\n用户主动中断了AI回复。',
              append: false
            })
          } else {
            // 如果有部分内容，在末尾添加中断标识
            this.$store.commit('chat/UPDATE_MESSAGE_CONTENT', {
              conversationId: this.conversationId,
              messageId: streamingMessage.id,
              content: '\n\n⏸️ *[回复已中断]*',
              append: true
            })
          }
        } else {
          console.log('🛑 未找到流式消息，可能在思考阶段被中断')
        }

        // 显示中断提示
        this.$message.warning(`AI回复已中断，耗时 ${this.formatElapsedTime(totalTime)}`)

      } catch (error) {
        console.error('中断AI回复失败:', error)
        this.$message.error('中断失败: ' + error.message)
        // 确保状态重置
        this.localLoading = false
      }
    },

    /**
     * 重试AI消息
     * 重新发送导致错误或中断的消息
     */
    async retryMessage(message) {
      try {
        console.log('🔄 开始重试AI消息:', message.id)

        // 检查是否正在加载中
        if (this.localLoading) {
          this.$message.warning('请等待当前请求完成')
          return
        }

        // 找到对应的用户消息（AI消息的前一条应该是用户消息）
        const messages = this.currentMessages
        const messageIndex = messages.findIndex(m => m.id === message.id)

        if (messageIndex <= 0) {
          this.$message.error('无法找到对应的用户消息')
          return
        }

        const userMessage = messages[messageIndex - 1]
        if (userMessage.type !== 'user') {
          this.$message.error('无法找到对应的用户消息')
          return
        }

        // 重置AI消息状态
        this.$store.commit('chat/UPDATE_MESSAGE_STATUS', {
          conversationId: this.conversationId,
          messageId: message.id,
          isStreaming: true,
          isInterrupted: false,
          isError: false,
          isComplete: false
        })

        // 清空AI消息内容，准备重新接收
        this.$store.commit('chat/UPDATE_MESSAGE_CONTENT', {
          conversationId: this.conversationId,
          messageId: message.id,
          content: '',
          append: false
        })

        // 设置加载状态和开始计时
        this.localLoading = true
        this.startTimer()

        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom()
        })

        // 重新发送用户消息
        console.log('🔄 重新发送用户消息:', userMessage.content)
        const result = await this.sendMessageToStore({
          conversationId: this.conversationId,
          content: userMessage.content,
          skipUserMessage: true, // 跳过添加用户消息，因为已经存在
          targetMessageId: message.id, // 指定要更新的AI消息ID
          // 传递回调函数来处理流式开始
          onStreamStart: () => {
            // 当开始接收流式数据时，隐藏loading状态
            this.localLoading = false
          }
        })

        // 停止计时
        const totalTime = this.stopTimer()
        console.log('📊 重试结果:', result)
        console.log('⏱️ 重试耗时:', totalTime, '秒')

        if (result && result.success) {
          this.$message.success(`重试成功，耗时 ${this.formatElapsedTime(totalTime)}`)
        } else if (result && result.success === false) {
          this.$message.error(`重试失败，耗时 ${this.formatElapsedTime(totalTime)}`)
        }

        // 滚动到底部显示结果
        this.$nextTick(() => {
          this.scrollToBottom()
        })

      } catch (error) {
        console.error("重试消息失败:", error)
        const totalTime = this.stopTimer()
        this.$message.error(`重试失败: ${error.message} (耗时 ${this.formatElapsedTime(totalTime)})`)
      } finally {
        // 重置加载状态
        this.localLoading = false
      }
    },

    /**
     * 清理SSE连接
     * 在组件销毁时调用，防止内存泄漏
     */
    cleanupSSEConnections() {
      // 动态导入SSE工具类并清理连接
      import('@/utils/sse').then(({ stopStreamChat }) => {
        // 停止当前对话的流式连接
        if (this.conversationId) {
          stopStreamChat(this.conversationId)
        }
      }).catch(error => {
        console.warn('清理SSE连接失败:', error)
      })
    }
  },

  /**
   * 组件销毁前的清理工作
   */
  beforeDestroy() {
    // 清理计时器，防止内存泄漏
    if (this.timerInterval) {
      clearInterval(this.timerInterval)
      this.timerInterval = null
    }
  },

  /**
   * Vue过滤器定义
   * 用于在模板中格式化数据显示
   */
  filters: {
    /**
     * 格式化时间戳为相对时间或绝对时间
     * @param {string} timestamp - ISO时间戳字符串
     * @returns {string} 格式化后的时间字符串
     */
    formatTime(timestamp) {
      if (!timestamp) return ''

      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date // 时间差（毫秒）

      // 根据时间差返回不同格式的时间显示
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 24小时内
        return `${Math.floor(diff / 3600000)}小时前`
      } else {
        // 超过24小时显示具体日期和时间
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString().slice(0, 5)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

.chat-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-primary);
}

.chat-info {
  flex: 1;
}

.chat-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.chat-time {
  font-size: var(--font-xs);
  color: var(--text-tertiary);
}

.chat-actions {
  display: flex;
  gap: 8px;
}



.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.empty-state {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 64px;
  color: var(--text-tertiary);
  margin-bottom: 16px;
}

.quick-questions {
  margin-top: 32px;
  max-width: 600px;

  h4 {
    font-size: var(--font-base);
    color: var(--text-secondary);
    margin-bottom: 16px;
  }
}

.question-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.question-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;

  &:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  i {
    font-size: 18px;
    color: var(--primary-color);
  }

  span {
    font-size: var(--font-sm);
    color: var(--text-primary);
    font-weight: 500;
  }
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message {
  display: flex;
  gap: 12px;
  max-width: 80%;
  
  &.message-user {
    align-self: flex-end;
    flex-direction: row-reverse;
  }
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  flex-shrink: 0;
  
  .message-user & {
    background: var(--primary-color);
  }
  
  .message-ai & {
    background: var(--success-color);
  }
}

.message-content {
  flex: 1;
  background: var(--bg-secondary);
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
  
  .message-user & {
    background: var(--primary-color);
    color: white;
  }
}

.message-text {
  line-height: 1.5;
  word-wrap: break-word;
  position: relative;

  &.typing-effect {
    overflow: hidden;
    border-right: 2px solid var(--primary-color);
    animation: typing 2s steps(40, end), blink-caret 0.75s step-end infinite;
  }

  &.streaming-effect {
    /* 流式输入时的样式 */
    position: relative;
  }

  &.error-message {
    color: #f56c6c;
    background: rgba(245, 108, 108, 0.1);
    padding: 8px;
    border-radius: 6px;
    border-left: 3px solid #f56c6c;
  }
}

/* 流式输入指示器 */
.streaming-indicator {
  display: inline-block;
  margin-left: 2px;
}

.cursor-blink {
  display: inline-block;
  color: var(--primary-color);
  font-weight: bold;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Markdown内容特殊样式 */
.message-text .markdown-renderer {
  white-space: normal; /* Markdown内容使用正常的空白处理 */
}

/* AI消息中的Markdown内容样式优化 */
.message-ai .message-text .markdown-renderer {
  /* 移除背景颜色，保持透明 */
  background: transparent;
  border-radius: 0;
  padding: 0;
  border: none;
  box-shadow: none;
}

/* Markdown代码块在AI消息中的特殊样式 */
.message-ai .message-text .markdown-renderer pre {
  background: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  margin: 8px 0 !important;
  border-radius: 6px !important;
}

/* Markdown行内代码在AI消息中的样式 */
.message-ai .message-text .markdown-renderer .inline-code {
  background: rgba(var(--primary-color-rgb), 0.15) !important;
  color: var(--primary-color) !important;
  font-weight: 600;
}

/* Markdown标题在AI消息中的样式 */
.message-ai .message-text .markdown-renderer .markdown-heading {
  color: var(--primary-color);
  margin-top: 16px;
  margin-bottom: 8px;
}

.message-ai .message-text .markdown-renderer .markdown-h1,
.message-ai .message-text .markdown-renderer .markdown-h2 {
  border-bottom-color: rgba(var(--primary-color-rgb), 0.2);
}

/* Markdown列表在AI消息中的样式 */
.message-ai .message-text .markdown-renderer .markdown-list {
  margin: 8px 0;
}

.message-ai .message-text .markdown-renderer .markdown-list-item {
  margin: 4px 0;
  color: var(--text-primary);
}

/* Markdown强调文本样式 */
.message-ai .message-text .markdown-renderer .markdown-strong {
  color: var(--primary-color);
  font-weight: 700;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--primary-color); }
}

.message-time {
  font-size: var(--font-xs);
  color: var(--text-tertiary);
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;

  .message-user & {
    color: rgba(255, 255, 255, 0.7);
  }
}

/* 响应时间显示 */
.response-time {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.15), rgba(var(--primary-color-rgb), 0.08));
  color: var(--primary-color);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
  box-shadow: 0 2px 4px rgba(var(--primary-color-rgb), 0.1);

  i {
    font-size: 11px;
    animation: pulse 2s ease-in-out infinite;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* 状态标识通用样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  i {
    font-size: 11px;
  }

  /* 流式输入状态 */
  &.streaming {
    background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.15), rgba(var(--primary-color-rgb), 0.08));
    color: var(--primary-color);
    border-color: rgba(var(--primary-color-rgb), 0.2);

    i {
      animation: spin 1s linear infinite;
    }
  }

  /* 中断状态 */
  &.interrupted {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 193, 7, 0.08));
    color: #ffc107;
    border-color: rgba(255, 193, 7, 0.2);
  }

  /* 错误状态 */
  &.error {
    background: linear-gradient(135deg, rgba(245, 108, 108, 0.15), rgba(245, 108, 108, 0.08));
    color: #f56c6c;
    border-color: rgba(245, 108, 108, 0.2);

    i {
      animation: shake 0.5s ease-in-out;
    }
  }

  /* 完成状态 */
  &.complete {
    background: linear-gradient(135deg, rgba(103, 194, 58, 0.15), rgba(103, 194, 58, 0.08));
    color: #67c23a;
    border-color: rgba(103, 194, 58, 0.2);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.message-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;

  .message:hover & {
    opacity: 1;
  }

  // 流式消息的操作按钮始终显示
  .message.message-ai .message-content:has(.streaming-effect) + & {
    opacity: 1;
  }
}



/* 大图标按钮通用样式 */
.large-icon {
  padding: 8px 12px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;

  i {
    font-size: 20px !important;
  }

  &:hover {
    transform: scale(1.1) !important;
  }
}

/* 流式消息中断按钮样式 */
.stop-message-btn {
  color: #f56c6c !important;
  background: rgba(245, 108, 108, 0.1) !important;
  border: 1px solid rgba(245, 108, 108, 0.3) !important;

  &:hover {
    color: #ffffff !important;
    background: #f56c6c !important;
    border-color: #f56c6c !important;
  }
}

/* 重试按钮样式 */
.retry-message-btn {
  color: var(--primary-color) !important;
  background: rgba(var(--primary-color-rgb), 0.1) !important;
  border: 1px solid rgba(var(--primary-color-rgb), 0.3) !important;

  &:hover {
    color: #ffffff !important;
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
  }

  i {
    animation: none !important;
  }

  &:hover i {
    animation: spin 0.5s linear !important;
  }
}

/* AI思考状态显示 */
.ai-thinking {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  border-radius: 16px;
  border: 2px solid rgba(var(--primary-color-rgb), 0.2);
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.1);
  position: relative;
  overflow: hidden;
}

.ai-thinking::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), #4facfe, var(--primary-color));
  background-size: 200% 100%;
  animation: shimmer 2s linear infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* AI思考区域头部 */
.thinking-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.thinking-text {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: var(--font-md);
  color: var(--primary-color);
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(var(--primary-color-rgb), 0.1);
}

/* 中断按钮样式 */
.stop-btn {
  color: #f56c6c !important;
  font-size: 14px !important;
  padding: 6px 12px !important;
  border: 1px solid rgba(245, 108, 108, 0.3) !important;
  border-radius: 6px !important;
  background: rgba(245, 108, 108, 0.1) !important;
  min-width: 60px !important;

  &:hover {
    color: #ffffff !important;
    background: #f56c6c !important;
    border-color: #f56c6c !important;
    transform: scale(1.05);
  }

  i {
    margin-right: 6px;
    font-size: 16px !important;
  }
}

.thinking-text i {
  animation: spin 1s linear infinite;
}

.thinking-timer {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: rgba(var(--primary-color-rgb), 0.1);
  border-radius: 20px;
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

.timer-label {
  font-size: var(--font-base);
  color: var(--text-primary);
  font-weight: 600;
}

.timer-value {
  font-size: var(--font-lg);
  color: var(--primary-color);
  font-family: 'Courier New', monospace;
  font-weight: 800;
  background: rgba(var(--primary-color-rgb), 0.2);
  padding: 6px 12px;
  border-radius: 12px;
  min-width: 70px;
  text-align: center;
  box-shadow: 0 2px 6px rgba(var(--primary-color-rgb), 0.15);
  border: 1px solid rgba(var(--primary-color-rgb), 0.3);
}

.typing-indicator {
  display: flex;
  gap: 4px;

  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    animation: typing 1.4s infinite ease-in-out;

    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.input-area {
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.send-btn {
  width: 44px;
  height: 44px;
  padding: 0;
  border-radius: 50%;
}

/* 输入区域的暂停按钮样式 */
.stop-input-btn {
  width: 44px;
  height: 44px;
  padding: 0;
  border-radius: 50%;
  background: #f56c6c !important;
  border-color: #f56c6c !important;

  &:hover {
    background: #f78989 !important;
    border-color: #f78989 !important;
    transform: scale(1.1);
  }

  i {
    font-size: 18px !important; /* 调大暂停图标 */
    color: white !important;
  }
}

/* 回到底部按钮 */
.scroll-to-bottom {
  position: fixed;
  right: 30px;
  bottom: 120px;
  width: 48px;
  height: 48px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
  transition: all 0.3s ease;
  z-index: 1000;

  &:hover {
    background: var(--primary-color-dark, #409eff);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(var(--primary-color-rgb), 0.4);
  }

  i {
    color: white;
    font-size: 20px;
    animation: bounce 2s infinite;
  }
}

/* 按钮动画 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-2px);
  }
}

/* 淡入淡出过渡效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.input-tips {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-xs);
  color: var(--text-tertiary);

  .tips-left {
    flex: 1;
  }

  .tips-right {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .vip-status {
    display: flex;
    align-items: center;
    gap: 8px;

    .vip-badge {
      i {
        margin-right: 4px;
      }
    }

    .free-user-badge {
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
      }

      i {
        margin-right: 4px;
      }
    }

    .upgrade-hint-btn {
      font-size: 12px;
      color: var(--warning-color);
      padding: 2px 6px;

      &:hover {
        color: var(--primary-color);
      }

      i {
        margin-right: 2px;
        font-size: 10px;
      }
    }

    .message-count {
      font-size: var(--font-xs);
      color: var(--text-secondary);
    }
  }
}

.limit-warning {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(245, 108, 108, 0.1);
  border: 1px solid rgba(245, 108, 108, 0.3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: var(--font-sm);
  color: #f56c6c;

  i {
    font-size: 1rem;
  }

  span {
    flex: 1;
  }

  .el-button {
    color: #f56c6c;
    font-weight: 600;

    &:hover {
      color: #f78989;
    }
  }
}

/* AI回复耗时显示样式 */
.ai-response-header {
  margin-bottom: 8px;
  font-size: var(--font-sm);
  color: var(--text-secondary);
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  border-radius: 8px;
  padding: 8px 12px;
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
  position: relative;
  overflow: hidden;
  max-width: 30%;
  width: fit-content;
  min-width: 200px;
}

.ai-timing-live, .ai-timing-completed {
  position: relative;
}

.timing-content {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.ai-timing-live {
  position: relative;
}

.ai-timing-live::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), #4facfe, var(--primary-color));
  background-size: 200% 100%;
  animation: shimmer 2s linear infinite;
}

.timing-icon-animated {
  animation: spin 1s linear infinite;
  color: var(--primary-color);
  font-size: 14px;
}

.timing-label {
  font-weight: 600;
  color: var(--text-primary);
}

.timing-prefix {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 12px;
}

.timing-value {
  font-family: 'Courier New', monospace;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 50px;
  text-align: center;
  border: 1px solid;
  font-size: 12px;
}

.timing-value.live {
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.2), rgba(var(--primary-color-rgb), 0.1));
  color: var(--primary-color);
  border-color: rgba(var(--primary-color-rgb), 0.3);
  box-shadow: 0 2px 6px rgba(var(--primary-color-rgb), 0.15);
  animation: pulse 2s ease-in-out infinite;
}

.timing-value.completed {
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.2), rgba(103, 194, 58, 0.1));
  color: #67c23a;
  border-color: rgba(103, 194, 58, 0.3);
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  border: 1px solid;

  &.complete {
    background: linear-gradient(135deg, rgba(103, 194, 58, 0.15), rgba(103, 194, 58, 0.08));
    color: #67c23a;
    border-color: rgba(103, 194, 58, 0.2);
  }

  &.interrupted {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 193, 7, 0.08));
    color: #ffc107;
    border-color: rgba(255, 193, 7, 0.2);
  }

  &.error {
    background: linear-gradient(135deg, rgba(245, 108, 108, 0.15), rgba(245, 108, 108, 0.08));
    color: #f56c6c;
    border-color: rgba(245, 108, 108, 0.2);
  }
}

.timing-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(var(--primary-color-rgb), 0.1);
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), #4facfe);
  width: 0%;
  animation: progress 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}
</style>
