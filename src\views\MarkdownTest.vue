<template>
  <div class="markdown-test">
    <h1>Markdown 渲染器测试</h1>
    
    <div class="test-container">
      <div class="input-section">
        <h3>输入 Markdown 内容：</h3>
        <el-input
          type="textarea"
          v-model="testMarkdown"
          :rows="20"
          placeholder="在这里输入 Markdown 内容进行测试..."
        />
      </div>
      
      <div class="output-section">
        <h3>渲染结果：</h3>
        <div class="rendered-output">
          <MarkdownRenderer :content="testMarkdown" :enable-highlight="true" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MarkdownRenderer from '@/components/MarkdownRenderer.vue'

export default {
  name: 'MarkdownTest',
  components: {
    MarkdownRenderer
  },
  data() {
    return {
      testMarkdown: `# 标题测试

## 二级标题

### 三级标题

#### 四级标题

##### 五级标题

###### 六级标题

## 文本格式

这是普通文本。

**这是粗体文本**

*这是斜体文本*

~~这是删除线文本~~

\`这是行内代码\`

## 列表

### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

### 有序列表
1. 第一项
2. 第二项
3. 第三项

### 任务列表
- [x] 已完成的任务
- [ ] 未完成的任务
- [x] 另一个已完成的任务
- [ ] 另一个未完成的任务

## 代码块

\`\`\`javascript
function hello() {
  console.log("Hello, World!");
  return "Hello from JavaScript!";
}

const arr = [1, 2, 3, 4, 5];
const doubled = arr.map(x => x * 2);
\`\`\`

\`\`\`python
def hello():
    print("Hello, World!")
    return "Hello from Python!"

numbers = [1, 2, 3, 4, 5]
doubled = [x * 2 for x in numbers]
\`\`\`

\`\`\`java
public class Hello {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}
\`\`\`

## 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |
| 数据7 | 数据8 | 数据9 |

## 引用

> 这是一个引用块
> 
> 可以包含多行内容
> 
> > 这是嵌套引用

## 链接和图片

[这是一个链接](https://www.example.com)

![这是一张图片](https://via.placeholder.com/300x200)

## 分隔线

---

## 混合内容

这里有一些 **粗体** 和 *斜体* 文本，还有 \`行内代码\`。

- [x] 支持任务列表
- [x] 支持代码高亮
- [x] 支持表格
- [ ] 继续改进功能

\`\`\`json
{
  "name": "测试",
  "version": "1.0.0",
  "features": [
    "markdown",
    "syntax-highlighting",
    "task-lists"
  ]
}
\`\`\``
    }
  }
}
</script>

<style lang="scss" scoped>
.markdown-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

.input-section, .output-section {
  h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
  }
}

.rendered-output {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  background: var(--bg-primary);
  min-height: 500px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .test-container {
    grid-template-columns: 1fr;
  }
}
</style>
