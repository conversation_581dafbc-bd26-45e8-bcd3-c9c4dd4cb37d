<template>
  <div class="markdown-renderer" v-html="renderedContent"></div>
</template>

<script>
import { marked } from 'marked'
import hljs from 'highlight.js/lib/core'

// 导入常用的语言支持
import javascript from 'highlight.js/lib/languages/javascript'
import java from 'highlight.js/lib/languages/java'
import python from 'highlight.js/lib/languages/python'
import css from 'highlight.js/lib/languages/css'
import html from 'highlight.js/lib/languages/xml'
import sql from 'highlight.js/lib/languages/sql'
import json from 'highlight.js/lib/languages/json'
// 注册语言
hljs.registerLanguage('javascript', javascript)
hljs.registerLanguage('js', javascript)
hljs.registerLanguage('java', java)
hljs.registerLanguage('python', python)
hljs.registerLanguage('py', python)
hljs.registerLanguage('css', css)
hljs.registerLanguage('html', html)
hljs.registerLanguage('xml', html)
hljs.registerLanguage('sql', sql)
hljs.registerLanguage('json', json)

export default {
  name: 'MarkdownRenderer',
  props: {
    // Markdown内容
    content: {
      type: String,
      default: ''
    },
    // 是否启用代码高亮
    enableHighlight: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    renderedContent() {
      if (!this.content) return ''

      try {
        // 配置 marked 选项
        const renderer = new marked.Renderer()

        // 自定义代码块渲染
        renderer.code = (code, language) => {
          if (this.enableHighlight && language && hljs.getLanguage(language)) {
            try {
              const highlighted = hljs.highlight(code, { language }).value
              return `<pre class="hljs"><code class="language-${language}">${highlighted}</code></pre>`
            } catch (err) {
              console.warn('代码高亮失败:', err)
            }
          }
          return `<pre><code class="language-${language || 'text'}">${this.escapeHtml(code)}</code></pre>`
        }

        // 自定义行内代码渲染
        renderer.codespan = (code) => {
          return `<code class="inline-code">${this.escapeHtml(code)}</code>`
        }

        // 自定义标题渲染
        renderer.heading = (text, level) => {
          const escapedText = text.toLowerCase().replace(/[^\w]+/g, '-')
          return `<h${level} class="markdown-heading markdown-h${level}" id="${escapedText}">${text}</h${level}>`
        }

        // 自定义列表渲染
        renderer.list = (body, ordered) => {
          const type = ordered ? 'ol' : 'ul'
          return `<${type} class="markdown-list">${body}</${type}>`
        }

        // 自定义列表项渲染
        renderer.listitem = (text) => {
          return `<li class="markdown-list-item">${text}</li>`
        }

        // 自定义段落渲染
        renderer.paragraph = (text) => {
          return `<p class="markdown-paragraph">${text}</p>`
        }

        // 自定义强调渲染
        renderer.strong = (text) => {
          return `<strong class="markdown-strong">${text}</strong>`
        }

        // 自定义斜体渲染
        renderer.em = (text) => {
          return `<em class="markdown-em">${text}</em>`
        }

        // 自定义删除线渲染
        renderer.del = (text) => {
          return `<del class="markdown-del">${text}</del>`
        }

        // 自定义链接渲染
        renderer.link = (href, title, text) => {
          const titleAttr = title ? ` title="${this.escapeHtml(title)}"` : ''
          return `<a href="${this.escapeHtml(href)}" class="markdown-link" target="_blank" rel="noopener noreferrer"${titleAttr}>${text}</a>`
        }

        // 自定义图片渲染
        renderer.image = (href, title, text) => {
          const titleAttr = title ? ` title="${this.escapeHtml(title)}"` : ''
          const altAttr = text ? ` alt="${this.escapeHtml(text)}"` : ''
          return `<img src="${this.escapeHtml(href)}" class="markdown-image"${altAttr}${titleAttr} loading="lazy" />`
        }

        // 自定义引用块渲染
        renderer.blockquote = (quote) => {
          return `<blockquote class="markdown-blockquote">${quote}</blockquote>`
        }

        // 自定义表格渲染
        renderer.table = (header, body) => {
          return `<div class="markdown-table-wrapper"><table class="markdown-table"><thead>${header}</thead><tbody>${body}</tbody></table></div>`
        }

        renderer.tablerow = (content) => {
          return `<tr class="markdown-table-row">${content}</tr>`
        }

        renderer.tablecell = (content, flags) => {
          const type = flags.header ? 'th' : 'td'
          const align = flags.align ? ` style="text-align: ${flags.align}"` : ''
          return `<${type} class="markdown-table-cell"${align}>${content}</${type}>`
        }

        // 自定义分隔线渲染
        renderer.hr = () => {
          return `<hr class="markdown-hr" />`
        }

        // 配置 marked
        marked.setOptions({
          renderer: renderer,
          gfm: true, // 启用GitHub风格的Markdown
          breaks: true, // 支持换行符转换为<br>
          sanitize: false, // 不清理HTML（我们会手动处理）
          smartLists: true,
          smartypants: true
        })

        let html = marked(this.content)

        // 后处理：支持任务列表（GitHub风格的checkbox）
        html = this.processTaskLists(html)

        return html
      } catch (error) {
        console.error('Markdown渲染失败:', error)
        return this.escapeHtml(this.content)
      }
    }
  },
  methods: {
    // HTML转义函数
    escapeHtml(text) {
      const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      }
      return text.replace(/[&<>"']/g, (m) => map[m])
    },

    // 处理任务列表（GitHub风格的checkbox）
    processTaskLists(html) {
      // 匹配任务列表项：- [ ] 或 - [x] 或 - [X]
      return html.replace(
        /<li class="markdown-list-item">\s*\[([ xX])\]\s*(.*?)<\/li>/g,
        (_, checked, content) => {
          const isChecked = checked.toLowerCase() === 'x'
          const checkboxId = 'task_' + Math.random().toString(36).substring(2, 11)
          return `<li class="markdown-task-item">
            <input type="checkbox" id="${checkboxId}" class="markdown-task-checkbox" ${isChecked ? 'checked' : ''} disabled>
            <label for="${checkboxId}" class="markdown-task-label">${content.trim()}</label>
          </li>`
        }
      )
    }
  }
}
</script>

<style scoped>
/* Markdown渲染器样式 */
.markdown-renderer {
  line-height: 1.6;
  color: var(--text-primary);
  word-wrap: break-word;
}

/* 标题样式 */
.markdown-heading {
  margin: 16px 0 12px 0;
  font-weight: 600;
  line-height: 1.4;
  color: var(--text-primary);
}

.markdown-h1 {
  font-size: 1.8em;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 8px;
}

.markdown-h2 {
  font-size: 1.5em;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 6px;
}

.markdown-h3 {
  font-size: 1.3em;
  color: var(--primary-color);
}

.markdown-h4 {
  font-size: 1.1em;
  color: var(--primary-color);
}

.markdown-h5, .markdown-h6 {
  font-size: 1em;
  color: var(--text-secondary);
}

/* 段落样式 */
.markdown-paragraph {
  margin: 12px 0;
  line-height: 1.7;
}

/* 列表样式 */
.markdown-list {
  margin: 12px 0;
  padding-left: 24px;
}

.markdown-list-item {
  margin: 6px 0;
  line-height: 1.6;
}

/* 强调样式 */
.markdown-strong {
  font-weight: 700;
  color: var(--primary-color);
}

.markdown-em {
  font-style: italic;
  color: var(--text-secondary);
}

/* 行内代码样式 */
.inline-code {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

/* 代码块样式 */
.markdown-renderer pre {
  background: #f8f9fa;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
  line-height: 1.5;
}

.markdown-renderer pre code {
  background: none;
  border: none;
  padding: 0;
  color: inherit;
}

/* 代码高亮样式 */
.hljs {
  background: #f8f9fa !important;
  color: #333;
}

/* 删除线样式 */
.markdown-del {
  text-decoration: line-through;
  color: var(--text-secondary);
  opacity: 0.7;
}

/* 链接样式 */
.markdown-link {
  color: var(--primary-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.markdown-link:hover {
  border-bottom-color: var(--primary-color);
  text-decoration: none;
}

/* 图片样式 */
.markdown-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 12px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.markdown-image:hover {
  transform: scale(1.02);
}

/* 引用块样式 */
.markdown-blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid var(--primary-color);
  background: rgba(var(--primary-color-rgb), 0.05);
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: var(--text-secondary);
}

.markdown-blockquote p {
  margin: 0;
}

/* 表格样式 */
.markdown-table-wrapper {
  overflow-x: auto;
  margin: 16px 0;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-primary);
}

.markdown-table-row:nth-child(even) {
  background: rgba(var(--primary-color-rgb), 0.02);
}

.markdown-table-cell {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  text-align: left;
  vertical-align: top;
}

.markdown-table thead .markdown-table-cell {
  background: rgba(var(--primary-color-rgb), 0.1);
  font-weight: 600;
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
}

/* 分隔线样式 */
.markdown-hr {
  margin: 24px 0;
  border: none;
  height: 2px;
  background: linear-gradient(to right, transparent, var(--border-color), transparent);
}

/* 任务列表样式 */
.markdown-task-item {
  display: flex;
  align-items: flex-start;
  margin: 8px 0;
  list-style: none;
  padding-left: 0;
}

.markdown-task-checkbox {
  margin-right: 8px;
  margin-top: 2px;
  cursor: default;
  accent-color: var(--primary-color);
}

.markdown-task-label {
  flex: 1;
  cursor: default;
  line-height: 1.6;
}

.markdown-task-checkbox:checked + .markdown-task-label {
  text-decoration: line-through;
  color: var(--text-secondary);
  opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-table-wrapper {
    font-size: 0.9em;
  }

  .markdown-table-cell {
    padding: 8px 12px;
  }

  .markdown-image {
    margin: 8px 0;
  }

  .markdown-blockquote {
    margin: 12px 0;
    padding: 8px 12px;
  }
}

</style>
